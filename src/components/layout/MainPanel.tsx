import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Terminal,
  Cpu,
  Zap,
  Mic,
  Play,
  Square,
  Volume2,
  Settings,
  Sparkles,
  Activity
} from 'lucide-react';

interface MainPanelProps {
  className?: string;
}

export function MainPanel({ className }: MainPanelProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isRecording, setIsRecording] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className={cn(
      "h-full flex flex-col space-y-6",
      className
    )}>
      {/* 主标题卡片 - 重新设计 */}
      <div className="relative group">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500" />
        <Card className="relative border-slate-700/50 bg-slate-900/40 backdrop-blur-xl shadow-2xl rounded-3xl overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-950/30 to-purple-950/30" />
          <CardHeader className="relative pb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <div className="relative">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center shadow-lg">
                    <Terminal className="w-8 h-8 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse shadow-lg" />
                </div>
                <div>
                  <CardTitle className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-wider bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                    EVERCALL
                  </CardTitle>
                  <p className="text-xl text-slate-400 font-mono uppercase tracking-[0.3em] mt-2">
                    智能语音合成系统
                  </p>
                </div>
              </div>

              {/* 状态指示器 */}
              <div className="flex flex-col items-end space-y-3">
                <Badge className="bg-green-500/20 text-green-400 border-green-500/30 px-4 py-2 text-sm font-medium">
                  <Activity className="w-4 h-4 mr-2 animate-pulse" />
                  系统在线
                </Badge>
                <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30 px-4 py-2 text-sm font-medium">
                  <Sparkles className="w-4 h-4 mr-2" />
                  v2.0.1
                </Badge>
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>

      {/* TTS控制面板 - 全新设计 */}
      <div className="flex-1 relative group">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-500/10 to-blue-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500" />
        <Card className="relative h-full border-slate-700/50 bg-slate-900/40 backdrop-blur-xl shadow-2xl rounded-3xl overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-slate-950/50 to-blue-950/30" />
          <CardHeader className="relative">
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl font-bold bg-gradient-to-r from-slate-200 to-blue-300 bg-clip-text text-transparent flex items-center">
                <Mic className="w-6 h-6 mr-3 text-blue-400" />
                语音合成控制台
              </CardTitle>
              <Button variant="outline" size="sm" className="border-slate-600 hover:border-blue-400 transition-colors">
                <Settings className="w-4 h-4 mr-2" />
                设置
              </Button>
            </div>
          </CardHeader>

          <CardContent className="relative h-full pb-8">
            <div className="h-full flex flex-col justify-center items-center space-y-8">
              {/* 主控制区域 */}
              <div className="text-center space-y-6">
                <div className="relative">
                  <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 border-2 border-blue-400/30 flex items-center justify-center backdrop-blur-sm">
                    <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center shadow-lg">
                      {isRecording ? (
                        <Square className="w-8 h-8 text-white" />
                      ) : (
                        <Play className="w-8 h-8 text-white ml-1" />
                      )}
                    </div>
                  </div>
                  {isRecording && (
                    <div className="absolute inset-0 rounded-full border-4 border-red-400/50 animate-ping" />
                  )}
                </div>

                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-slate-200">
                    {isRecording ? '正在录制...' : '准备就绪'}
                  </h3>
                  <p className="text-slate-400">
                    点击开始录制或输入文本进行语音合成
                  </p>
                </div>
              </div>

              {/* 控制按钮组 */}
              <div className="flex items-center space-x-4">
                <Button
                  size="lg"
                  className={cn(
                    "px-8 py-4 rounded-2xl font-medium transition-all duration-300",
                    isRecording
                      ? "bg-red-500 hover:bg-red-600 text-white shadow-lg shadow-red-500/25"
                      : "bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-lg shadow-blue-500/25"
                  )}
                  onClick={() => setIsRecording(!isRecording)}
                >
                  {isRecording ? (
                    <>
                      <Square className="w-5 h-5 mr-2" />
                      停止录制
                    </>
                  ) : (
                    <>
                      <Mic className="w-5 h-5 mr-2" />
                      开始录制
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  size="lg"
                  className="px-8 py-4 rounded-2xl border-slate-600 hover:border-blue-400 hover:bg-blue-500/10 transition-all duration-300"
                >
                  <Volume2 className="w-5 h-5 mr-2" />
                  播放
                </Button>
              </div>

              {/* 状态信息 */}
              <div className="text-center space-y-2">
                <p className="text-sm text-slate-500">
                  Live2D 数字助手已就绪，等待语音指令
                </p>
                <div className="flex items-center justify-center space-x-2 text-xs text-slate-600">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span>AI 模型已加载</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 底部状态栏 - 重新设计 */}
      <div className="flex items-center justify-between p-4 bg-slate-900/60 backdrop-blur-xl rounded-2xl border border-slate-700/50">
        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" />
            <span className="text-sm font-medium text-green-400">READY</span>
          </div>

          <div className="flex items-center space-x-3">
            <span className="text-sm text-slate-400">CORE</span>
            <div className="w-16 h-2 bg-slate-700 rounded-full overflow-hidden">
              <div className="w-3/4 h-full bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse" />
            </div>
            <span className="text-sm text-slate-300 font-medium">75%</span>
          </div>
        </div>

        <div className="text-sm font-mono text-slate-400">
          {currentTime.toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
          })}
        </div>
      </div>
    </div>
  );
}
