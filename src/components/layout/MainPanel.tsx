import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Terminal, Cpu, Zap } from 'lucide-react';


interface MainPanelProps {
  className?: string;
}

export function MainPanel({ className }: MainPanelProps) {
  return (
    <div className={cn(
      "h-full flex flex-col space-y-6",
      className
    )}>
      {/* 标题区域 */}
      <Card className="border-primary/20 bg-background/95 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Terminal className="w-8 h-8 text-primary" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-cyber-green rounded-full animate-pulse" />
              </div>
              <div>
                <CardTitle className="text-4xl md:text-5xl lg:text-6xl font-orbitron tracking-wider text-primary">
                  EVERCALL
                </CardTitle>
                <p className="text-lg text-muted-foreground font-mono uppercase tracking-widest mt-1">
                  TTS Terminal System
                </p>
              </div>
            </div>

            {/* 状态指示器 */}
            <div className="flex flex-col items-end space-y-2">
              <Badge variant="terminal" className="animate-pulse-glow">
                <Cpu className="w-3 h-3 mr-1" />
                System Online
              </Badge>
              <Badge variant="cyber">
                <Zap className="w-3 h-3 mr-1" />
                v2.0.1
              </Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* TTS功能区域占位 */}
      <Card className="flex-1 border-secondary/20 bg-background/95 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-secondary font-orbitron">
            语音合成模块
          </CardTitle>
        </CardHeader>
        <CardContent className="h-full">
          {/* TTS组件将在后续任务中实现 */}
          <div className="h-full flex items-center justify-center">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 mx-auto rounded-full border-2 border-dashed border-secondary/50 flex items-center justify-center">
                <Terminal className="w-8 h-8 text-secondary/50" />
              </div>
              <p className="text-muted-foreground font-mono">
                TTS功能组件即将实现...
              </p>
              <p className="text-xs text-muted-foreground">
                Live2D 模型现在显示在右下角
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 底部状态栏 */}
      <div className="flex items-center justify-between text-xs font-mono text-muted-foreground">
        <div className="flex items-center space-x-4">
          <span>READY</span>
          <div className="w-2 h-2 bg-cyber-green rounded-full animate-pulse" />
        </div>

        <div className="flex items-center space-x-2">
          <span>CORE</span>
          <div className="w-12 h-1 bg-border rounded-full overflow-hidden">
            <div className="w-3/4 h-full bg-primary animate-pulse" />
          </div>
          <span>75%</span>
        </div>

        <div className="hidden sm:block">
          {new Date().toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
          })}
        </div>
      </div>
    </div>
  );
}
