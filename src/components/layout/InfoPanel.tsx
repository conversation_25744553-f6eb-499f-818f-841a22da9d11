import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import {
  Database,
  FileText,
  Settings,
  Activity,
  Cpu,
  HardDrive,
  Wifi,
  TrendingUp,
  Zap,
  Clock
} from 'lucide-react';

interface InfoPanelProps {
  className?: string;
}

export function InfoPanel({ className }: InfoPanelProps) {
  return (
    <div className={cn(
      "space-y-6",
      className
    )}>
      {/* 系统状态卡片 */}
      <div className="relative group">
        <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-500" />
        <Card className="relative border-slate-700/50 bg-slate-900/40 backdrop-blur-xl shadow-xl rounded-2xl">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent flex items-center">
              <Activity className="w-5 h-5 mr-2 text-green-400" />
              系统状态
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 连接状态 */}
            <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-xl">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center">
                  <Wifi className="w-5 h-5 text-blue-400" />
                </div>
                <span className="text-sm font-medium text-slate-200">WebSocket</span>
              </div>
              <Badge className="bg-red-500/20 text-red-400 border-red-500/30">
                未连接
              </Badge>
            </div>

            {/* Live2D状态 */}
            <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-xl">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-purple-400" />
                </div>
                <span className="text-sm font-medium text-slate-200">Live2D</span>
              </div>
              <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                已加载
              </Badge>
            </div>

            {/* 性能状态 */}
            <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-xl">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-orange-400" />
                </div>
                <span className="text-sm font-medium text-slate-200">性能</span>
              </div>
              <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                最佳
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 音频缓存面板 */}
      <div className="relative group">
        <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-yellow-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-500" />
        <Card className="relative border-slate-700/50 bg-slate-900/40 backdrop-blur-xl shadow-xl rounded-2xl">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-bold bg-gradient-to-r from-orange-400 to-yellow-400 bg-clip-text text-transparent flex items-center">
                <Database className="w-5 h-5 mr-2 text-orange-400" />
                音频缓存
              </CardTitle>
              <Badge className="bg-orange-500/20 text-orange-400 border-orange-500/30">
                0/10
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto bg-gradient-to-br from-orange-500/20 to-yellow-500/20 rounded-2xl flex items-center justify-center mb-4">
                <Database className="w-8 h-8 text-orange-400/60" />
              </div>
              <div className="text-sm font-medium text-slate-300 mb-1">暂无缓存音频</div>
              <div className="text-xs text-slate-500">音频文件将显示在这里</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 系统日志面板 */}
      <div className="relative group">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-500" />
        <Card className="relative border-slate-700/50 bg-slate-900/40 backdrop-blur-xl shadow-xl rounded-2xl">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent flex items-center">
                <FileText className="w-5 h-5 mr-2 text-blue-400" />
                系统日志
              </CardTitle>
              <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30 flex items-center gap-1">
                <Activity className="w-3 h-3 animate-pulse" />
                实时
              </Badge>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            <ScrollArea className="h-48 px-4">
              <div className="space-y-3 py-2">
                {/* 示例日志条目 */}
                <div className="space-y-2">
                  <div className="flex items-start gap-3 p-2 bg-slate-800/30 rounded-lg">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2 animate-pulse" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 text-xs text-slate-400 mb-1">
                        <Clock className="w-3 h-3" />
                        <span>12:34:56</span>
                        <span className="px-2 py-0.5 bg-green-500/20 text-green-400 rounded">信息</span>
                      </div>
                      <div className="text-sm text-slate-200">Live2D 服务已初始化</div>
                    </div>
                  </div>

                  <div className="flex items-start gap-3 p-2 bg-slate-800/30 rounded-lg">
                    <div className="w-2 h-2 bg-blue-400 rounded-full mt-2" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 text-xs text-slate-400 mb-1">
                        <Clock className="w-3 h-3" />
                        <span>12:34:57</span>
                        <span className="px-2 py-0.5 bg-blue-500/20 text-blue-400 rounded">信息</span>
                      </div>
                      <div className="text-sm text-slate-200">模型配置已加载</div>
                    </div>
                  </div>

                  <div className="flex items-start gap-3 p-2 bg-slate-800/30 rounded-lg">
                    <div className="w-2 h-2 bg-orange-400 rounded-full mt-2" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 text-xs text-slate-400 mb-1">
                        <Clock className="w-3 h-3" />
                        <span>12:34:58</span>
                        <span className="px-2 py-0.5 bg-orange-500/20 text-orange-400 rounded">警告</span>
                      </div>
                      <div className="text-sm text-slate-200">未指定默认模型</div>
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>

            <div className="p-4 border-t border-slate-700/50 bg-slate-800/30">
              <div className="text-xs text-slate-400 text-center font-mono flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                系统就绪 • Live2D 已加载 • WebSocket 等待连接
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 快捷键提示面板 */}
      <div className="relative group">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-500" />
        <Card className="relative border-slate-700/50 bg-slate-900/40 backdrop-blur-xl shadow-xl rounded-2xl">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent flex items-center">
              <Settings className="w-5 h-5 mr-2 text-purple-400" />
              快捷键
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <div className="flex items-center justify-between p-2 bg-slate-800/30 rounded-lg">
                <span className="text-sm text-slate-200">随机表情</span>
                <kbd className="px-2 py-1 bg-slate-700 text-slate-300 rounded text-xs font-mono">空格</kbd>
              </div>
              <div className="flex items-center justify-between p-2 bg-slate-800/30 rounded-lg">
                <span className="text-sm text-slate-200">随机动作</span>
                <kbd className="px-2 py-1 bg-slate-700 text-slate-300 rounded text-xs font-mono">回车</kbd>
              </div>
              <div className="flex items-center justify-between p-2 bg-slate-800/30 rounded-lg">
                <span className="text-sm text-slate-200">重置模型</span>
                <kbd className="px-2 py-1 bg-slate-700 text-slate-300 rounded text-xs font-mono">R</kbd>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
