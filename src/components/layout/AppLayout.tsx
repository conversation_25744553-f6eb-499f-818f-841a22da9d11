import React from 'react';
import { cn } from '@/lib/utils';
import { MainPanel } from './MainPanel';
import { InfoPanel } from './InfoPanel';
import { GridBackground } from './GridBackground';
import { SimpleLive2D } from '@/components/SimpleLive2D';

interface AppLayoutProps {
  className?: string;
}

export function AppLayout({ className }: AppLayoutProps) {
  return (
    <div className={cn(
      "min-h-screen relative overflow-hidden",
      "bg-gradient-to-br from-slate-950 via-blue-950 to-purple-950",
      className
    )}>
      {/* 动态背景 */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-slate-900/10 to-transparent" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_right,_var(--tw-gradient-stops))] from-purple-900/20 via-slate-900/10 to-transparent" />

      {/* 网格背景 */}
      <GridBackground />

      {/* 浮动粒子效果 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-blue-400/30 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`,
            }}
          />
        ))}
      </div>

      {/* 主内容区域 - 新的网格布局 */}
      <div className="relative z-10 min-h-screen p-6 grid grid-cols-12 gap-6">
        {/* 左侧主控制面板 */}
        <div className="col-span-12 lg:col-span-7 xl:col-span-8 space-y-6">
          <MainPanel />
        </div>

        {/* 右侧Live2D和状态面板 */}
        <div className="col-span-12 lg:col-span-5 xl:col-span-4 space-y-6">
          {/* Live2D展示区域 */}
          <div className="relative">
            <div className="bg-gradient-to-br from-slate-900/40 to-slate-800/40 backdrop-blur-xl border border-slate-700/50 rounded-3xl p-6 shadow-2xl">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  数字助手
                </h3>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span className="text-sm text-slate-400">在线</span>
                </div>
              </div>

              {/* Live2D容器 */}
              <div className="relative bg-gradient-to-br from-blue-950/30 to-purple-950/30 rounded-2xl overflow-hidden border border-slate-600/30">
                <SimpleLive2D
                  width={350}
                  height={450}
                  modelPath="/static/live2d/models/idol/ldol.model3.json"
                />

                {/* 装饰性边框 */}
                <div className="absolute inset-0 rounded-2xl border-2 border-gradient-to-br from-blue-400/20 to-purple-400/20 pointer-events-none" />
              </div>
            </div>
          </div>

          {/* 信息面板 */}
          <InfoPanel />
        </div>
      </div>

      {/* 增强的装饰效果 */}
      <div className="fixed top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-400/50 to-transparent z-50" />
      <div className="fixed bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-400/50 to-transparent z-50" />

      {/* 动态光效 */}
      <div className="fixed top-10 left-10 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse z-0" />
      <div className="fixed top-20 right-20 w-48 h-48 bg-purple-500/10 rounded-full blur-3xl animate-pulse z-0" style={{ animationDelay: '1s' }} />
      <div className="fixed bottom-20 left-20 w-56 h-56 bg-cyan-500/10 rounded-full blur-3xl animate-pulse z-0" style={{ animationDelay: '2s' }} />
    </div>
  );
}
