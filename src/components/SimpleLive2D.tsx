import React, { useEffect, useRef, useState } from 'react';
import * as PIXI from 'pixi.js';
import { Live2DModel } from 'pixi-live2d-display';

// 确保 PIXI 全局可用
window.PIXI = PIXI;

interface SimpleLive2DProps {
  width?: number;
  height?: number;
  modelPath?: string;
  className?: string;
}

export function SimpleLive2D({
  width = 400,
  height = 600,
  modelPath = '/static/live2d/models/idol/ldol.model3.json',
  className = ''
}: SimpleLive2DProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const appRef = useRef<PIXI.Application | null>(null);
  const modelRef = useRef<Live2DModel | null>(null);
  const [status, setStatus] = useState<string>('初始化中...');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;

    const initLive2D = async () => {
      if (!containerRef.current) return;

      // 防止重复初始化
      if (appRef.current) {
        console.log('PIXI 应用已存在，跳过初始化');
        return;
      }

      try {
        setStatus('创建 PIXI 应用...');
        setError(null);
        console.log('开始初始化 Live2D');

        // 创建 PIXI 应用 (PIXI v6 API)
        const app = new PIXI.Application({
          width,
          height,
          backgroundAlpha: 0,
          antialias: true,
          resolution: window.devicePixelRatio || 1,
        });

        if (!mounted) return;

        // 等待应用初始化完成
        await app.init?.() || Promise.resolve();

        if (!mounted) return;

        // 添加 canvas 到容器 (PIXI v6 使用 app.view)
        const canvas = app.view || app.canvas;
        if (!canvas) {
          throw new Error('无法获取 PIXI canvas');
        }

        console.log('Canvas 类型:', canvas.constructor.name);
        containerRef.current.appendChild(canvas);
        appRef.current = app;

        console.log('PIXI 应用创建成功');
        setStatus('加载 Live2D 模型...');

        // 加载 Live2D 模型
        console.log('开始加载模型:', modelPath);
        const model = await Live2DModel.from(modelPath);

        if (!mounted) return;

        console.log('模型加载成功');
        modelRef.current = model;

        // 设置模型位置和大小
        model.scale.set(0.5); // 增大模型尺寸
        model.x = width / 2;
        model.y = height - 50; // 稍微上移，确保完全可见

        // 添加模型到舞台
        app.stage.addChild(model);

        console.log('模型已添加到舞台');

        // 等待一帧后设置状态，确保渲染完成
        requestAnimationFrame(() => {
          setStatus('Live2D 模型加载完成！');
        });

        // 添加点击交互
        model.on('hit', (hitAreas: string[]) => {
          console.log('点击了:', hitAreas);
          if (hitAreas.includes('body')) {
            model.motion('tap_body');
          }
        });

        // 播放默认动作
        setTimeout(() => {
          try {
            model.motion('idle');
          } catch (error) {
            console.warn('播放默认动作失败:', error);
          }
        }, 1000);

      } catch (err) {
        console.error('Live2D 初始化失败:', err);
        setError(err instanceof Error ? err.message : '未知错误');
        setStatus('初始化失败');
      }
    };

    initLive2D();

    // 清理函数
    return () => {
      mounted = false;

      // 清理模型
      if (modelRef.current) {
        try {
          modelRef.current.destroy();
        } catch (error) {
          console.warn('清理模型时出错:', error);
        }
        modelRef.current = null;
      }

      // 清理 PIXI 应用
      if (appRef.current) {
        try {
          // 清理 canvas
          const canvas = appRef.current.view || appRef.current.canvas;
          if (canvas && canvas.parentNode) {
            canvas.parentNode.removeChild(canvas);
          }

          appRef.current.destroy(true, {
            children: true,
            texture: true,
            baseTexture: true
          });
        } catch (error) {
          console.warn('清理 PIXI 应用时出错:', error);
        }
        appRef.current = null;
      }
    };
  }, [width, height, modelPath]);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      if (appRef.current) {
        appRef.current.renderer.resize(width, height);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [width, height]);

  return (
    <div className={`live2d-simple ${className}`}>
      <div
        ref={containerRef}
        style={{
          width: `${width}px`,
          height: `${height}px`,
          border: '2px solid #333',
          borderRadius: '8px',
          overflow: 'hidden',
          position: 'relative',
          background: 'linear-gradient(135deg, #2a2a3a 0%, #1a1a2e 100%)',
        }}
      >
        {/* 状态显示 */}
        {(status !== 'Live2D 模型加载完成！') && (
          <div
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              color: 'white',
              textAlign: 'center',
              zIndex: 10,
              background: 'rgba(0,0,0,0.7)',
              padding: '10px',
              borderRadius: '5px',
            }}
          >
            <div>{status}</div>
            {error && <div style={{ color: '#ff6b6b', marginTop: '5px' }}>{error}</div>}
          </div>
        )}
      </div>

      {/* 控制按钮 */}
      <div style={{ marginTop: '10px', textAlign: 'center' }}>
        <button
          onClick={() => {
            try {
              modelRef.current?.motion('idle');
            } catch (error) {
              console.warn('播放待机动作失败:', error);
            }
          }}
          style={{
            margin: '0 5px',
            padding: '5px 10px',
            background: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer',
          }}
        >
          待机
        </button>
        <button
          onClick={() => {
            try {
              modelRef.current?.motion('tap_body');
            } catch (error) {
              console.warn('播放动作失败:', error);
            }
          }}
          style={{
            margin: '0 5px',
            padding: '5px 10px',
            background: '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer',
          }}
        >
          动作
        </button>
        <button
          onClick={() => {
            try {
              modelRef.current?.expression('f01');
            } catch (error) {
              console.warn('播放表情失败:', error);
            }
          }}
          style={{
            margin: '0 5px',
            padding: '5px 10px',
            background: '#FF9800',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer',
          }}
        >
          表情
        </button>
      </div>
    </div>
  );
}
