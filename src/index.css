@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@400;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 204 100% 50%; /* #00a2ff */
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 24 100% 50%; /* #ff6600 */
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 204 100% 50%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    margin: 0;
    min-height: 100vh;
    font-family: 'Rajdhani', ui-sans-serif, system-ui;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #00a2ff, #8b5cf6);
    border-radius: 4px;
    border: 1px solid hsl(var(--border));
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #00a2ff, #00ff88);
    box-shadow: 0 0 10px rgba(0, 162, 255, 0.3);
  }

  /* 选择文本样式 */
  ::selection {
    background: rgba(0, 162, 255, 0.3);
    color: hsl(var(--foreground));
  }

  /* 焦点样式 */
  :focus-visible {
    outline: 2px solid #00a2ff;
    outline-offset: 2px;
  }
}

@layer components {
  /* 网格背景 */
  .grid-background {
    background-image:
      linear-gradient(rgba(0, 255, 136, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 255, 136, 0.1) 1px, transparent 1px);
    background-size: 60px 60px;
    background-position: 0 0, 0 0;
  }

  /* 科技感边框 */
  .cyber-border {
    position: relative;
  }

  .cyber-border::before,
  .cyber-border::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid #00a2ff;
  }

  .cyber-border::before {
    top: -1px;
    left: -1px;
    border-right: none;
    border-bottom: none;
  }

  .cyber-border::after {
    bottom: -1px;
    right: -1px;
    border-left: none;
    border-top: none;
  }

  /* 终端发光效果 */
  .terminal-glow {
    box-shadow:
      0 0 10px rgba(0, 255, 136, 0.3),
      inset 0 0 10px rgba(0, 255, 136, 0.1);
  }

  /* 处理中动画 */
  .processing-animation {
    animation: pulseGlow 2s ease-in-out infinite;
  }

  /* 扫描线效果 */
  .scan-line {
    position: relative;
    overflow: hidden;
  }

  .scan-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(0, 162, 255, 0.1),
      transparent
    );
    animation: scanLine 2s linear infinite;
  }

  /* 数据流效果 */
  .data-stream {
    position: relative;
    overflow: hidden;
  }

  .data-stream::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg,
      transparent,
      #00a2ff,
      transparent
    );
    animation: dataFlow 3s linear infinite;
  }

  /* 全息效果 */
  .hologram {
    background: linear-gradient(
      135deg,
      rgba(0, 162, 255, 0.1) 0%,
      rgba(139, 92, 246, 0.1) 50%,
      rgba(0, 255, 136, 0.1) 100%
    );
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 162, 255, 0.2);
  }

  /* 矩阵雨效果 */
  .matrix-rain {
    position: relative;
    overflow: hidden;
  }

  .matrix-rain::before {
    content: '01010101';
    position: absolute;
    top: -100%;
    left: 0;
    right: 0;
    color: rgba(0, 255, 136, 0.3);
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 14px;
    letter-spacing: 2px;
    animation: matrixRain 20s linear infinite;
    pointer-events: none;
  }
}

@layer utilities {
  /* 渐变文本 */
  .gradient-text {
    background: linear-gradient(135deg, #00a2ff, #8b5cf6, #00ff88);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 发光文本 */
  .glow-text {
    text-shadow:
      0 0 10px currentColor,
      0 0 20px currentColor,
      0 0 40px currentColor;
  }

  /* 打字机效果 */
  .typewriter {
    overflow: hidden;
    border-right: 2px solid #00a2ff;
    white-space: nowrap;
    margin: 0 auto;
    animation:
      typing 3.5s steps(40, end),
      blink-caret 0.75s step-end infinite;
  }

  /* 隐藏滚动条但保持功能 */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* 径向渐变 */
  .bg-gradient-radial {
    background: radial-gradient(var(--tw-gradient-stops));
  }

  /* 锥形渐变 */
  .bg-gradient-conic {
    background: conic-gradient(var(--tw-gradient-stops));
  }
}

/* 额外的动画关键帧 */
@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: #00a2ff }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes slideInFromBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 新增现代化动画效果 */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
    transform: scale(1.02);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotateGlow {
  0% {
    transform: rotate(0deg);
    filter: hue-rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
    filter: hue-rotate(360deg);
  }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) translateX(-5px);
    opacity: 1;
  }
  75% {
    transform: translateY(-30px) translateX(15px);
    opacity: 0.6;
  }
}

/* 响应式字体大小 */
@media (max-width: 640px) {
  body {
    font-size: 14px;
  }
}

@media (min-width: 1024px) {
  body {
    font-size: 16px;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
